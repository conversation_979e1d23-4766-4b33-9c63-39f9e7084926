<?php $__env->startSection('content'); ?>
<div class="min-h-screen flex">
    <!-- Department Sidebar -->
    <aside class="w-64 bg-white shadow-md">
        <div class="p-4 border-b">
            <img src="<?php echo e(asset('images/ucua-logo.png')); ?>" alt="UCUA Logo" class="h-12 mx-auto">
            <h2 class="text-xl font-bold text-center text-gray-800 mt-2"><?php echo e(auth()->guard('department')->user()->name); ?> Dashboard</h2>
        </div>

        <nav class="mt-6">
            <ul class="space-y-2">
                <li>
                    <a href="<?php echo e(route('department.dashboard')); ?>"
                       class="flex items-center px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">
                        <i class="fas fa-chart-line w-5"></i>
                        <span>Report Overview</span>
                    </a>
                </li>
                <li>
                    <a href="<?php echo e(route('department.pending-reports')); ?>"
                       class="flex items-center px-4 py-2 text-gray-600 hover:bg-yellow-100 hover:text-yellow-800">
                        <i class="fas fa-clock w-5"></i>
                        <span>Pending Reports</span>
                    </a>
                </li>
                <li>
                    <a href="<?php echo e(route('department.resolved-reports')); ?>"
                       class="flex items-center px-4 py-2 text-gray-600 hover:bg-green-100 hover:text-green-800">
                        <i class="fas fa-check-circle w-5"></i>
                        <span>Resolved Reports</span>
                    </a>
                </li>
                <li>
                    <a href="<?php echo e(route('department.notifications')); ?>"
                       class="flex items-center px-4 py-2 <?php echo e(Request::routeIs('department.notifications') ? 'bg-red-50 text-red-600' : 'text-gray-600'); ?> hover:bg-red-100 hover:text-red-700 transition-colors duration-200">
                        <i class="fas fa-bell w-5"></i>
                        <span class="ml-2">Notifications</span>
                    </a>
                </li>
                <li>
                    <a href="<?php echo e(route('help.department')); ?>"
                       class="flex items-center px-4 py-2 text-gray-600 hover:bg-blue-50 hover:text-blue-600">
                        <i class="fas fa-question-circle w-5"></i>
                        <span>Help</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <div class="flex-1 flex flex-col">
        <!-- Header -->
        <header class="bg-blue-800 text-white p-4 shadow-md">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">Notifications</h1>
                <div class="flex items-center space-x-4">
                    <span>Welcome, <?php echo e(auth()->guard('department')->user()->head_name); ?></span>
                    <form action="<?php echo e(route('department.logout')); ?>" method="POST" class="inline">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-white hover:text-gray-200">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </form>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 p-6 bg-gray-100">
            <!-- Success/Error Messages -->
            <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 alert alert-success">
                <?php echo e(session('success')); ?>

            </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 alert alert-danger">
                <?php echo e(session('error')); ?>

            </div>
            <?php endif; ?>

            <!-- Notifications Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 flex items-center">
                            <i class="fas fa-bell mr-3 text-red-500"></i>
                            All Notifications
                        </h2>
                        <p class="text-gray-600 mt-1">Stay updated with reminders and important messages</p>
                    </div>
                    <div class="flex space-x-3">
                        <?php if($notifications->where('read_at', null)->count() > 0): ?>
                        <form action="<?php echo e(route('department.notifications.mark-all-read')); ?>" method="POST" class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-check-double mr-2"></i>
                                Mark All Read
                            </button>
                        </form>
                        <?php endif; ?>
                        <a href="<?php echo e(route('department.dashboard')); ?>" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6">
                    <?php if($notifications->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-l-4 <?php echo e($notification->read_at ? 'border-gray-300 bg-gray-50' : 'border-red-500 bg-red-50'); ?> p-6 rounded-r-lg transition-all duration-200 hover:shadow-md">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <?php if(isset($notification->data['type']) && $notification->data['type'] === 'reminder'): ?>
                                    <!-- Reminder Notification -->
                                    <div class="flex items-center mb-3">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                            <?php echo e($notification->data['reminder_type'] === 'gentle' ? 'bg-green-100 text-green-800' : 
                                               ($notification->data['reminder_type'] === 'urgent' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800')); ?>">
                                            <i class="fas fa-bell mr-2"></i>
                                            <?php echo e(ucfirst($notification->data['reminder_type'])); ?> Reminder
                                        </span>
                                        <span class="ml-3 text-sm text-gray-500 font-mono"><?php echo e($notification->data['reminder_formatted_id'] ?? ''); ?></span>
                                        <?php if(!$notification->read_at): ?>
                                        <span class="ml-3 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-circle mr-1 text-blue-500" style="font-size: 6px;"></i>
                                            New
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                            Report: <?php echo e($notification->data['report_formatted_id'] ?? 'N/A'); ?>

                                        </h3>
                                        <p class="text-gray-700 mb-2"><?php echo e($notification->data['report_description'] ?? ''); ?></p>
                                        
                                        <?php if(isset($notification->data['report_location'])): ?>
                                        <p class="text-sm text-gray-600 mb-1">
                                            <i class="fas fa-map-marker-alt mr-1"></i>
                                            Location: <?php echo e($notification->data['report_location']); ?>

                                        </p>
                                        <?php endif; ?>
                                        
                                        <?php if(isset($notification->data['report_deadline'])): ?>
                                        <p class="text-sm text-gray-600 mb-2">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            Deadline: <?php echo e($notification->data['report_deadline']); ?>

                                        </p>
                                        <?php endif; ?>
                                    </div>

                                    <?php if(isset($notification->data['message']) && $notification->data['message']): ?>
                                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 mb-3">
                                        <p class="text-sm text-blue-800 italic">
                                            <i class="fas fa-quote-left mr-1"></i>
                                            "<?php echo e($notification->data['message']); ?>"
                                        </p>
                                        <p class="text-xs text-blue-600 mt-1">
                                            - <?php echo e($notification->data['sent_by'] ?? 'UCUA Officer'); ?>

                                        </p>
                                    </div>
                                    <?php endif; ?>

                                    <div class="bg-yellow-50 border border-yellow-200 rounded p-3">
                                        <p class="text-sm text-yellow-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            <?php echo e($notification->data['action_required'] ?? 'Please review and take appropriate action on this safety report.'); ?>

                                        </p>
                                    </div>
                                    <?php else: ?>
                                    <!-- Other Notification Types -->
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                        <span class="font-medium text-gray-900">General Notification</span>
                                        <?php if(!$notification->read_at): ?>
                                        <span class="ml-3 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-circle mr-1 text-blue-500" style="font-size: 6px;"></i>
                                            New
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="text-gray-700"><?php echo e($notification->data['message'] ?? 'New notification received'); ?></p>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="text-right ml-6 flex-shrink-0">
                                    <p class="text-sm text-gray-500 mb-2"><?php echo e($notification->created_at->format('M d, Y')); ?></p>
                                    <p class="text-xs text-gray-400 mb-3"><?php echo e($notification->created_at->format('h:i A')); ?></p>
                                    
                                    <div class="flex flex-col space-y-2">
                                        <?php if(!$notification->read_at): ?>
                                        <button onclick="markAsRead('<?php echo e($notification->id); ?>')" 
                                                class="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors">
                                            Mark as Read
                                        </button>
                                        <?php else: ?>
                                        <span class="text-xs text-green-600 px-3 py-1 bg-green-100 rounded">
                                            <i class="fas fa-check mr-1"></i>
                                            Read
                                        </span>
                                        <?php endif; ?>
                                        
                                        <?php if(isset($notification->data['link'])): ?>
                                        <a href="<?php echo e($notification->data['link']); ?>" 
                                           class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600 transition-colors text-center">
                                            View Report
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        <?php echo e($notifications->links()); ?>

                    </div>
                    <?php else: ?>
                    <div class="text-center py-12">
                        <i class="fas fa-bell-slash text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-medium text-gray-500 mb-2">No Notifications</h3>
                        <p class="text-gray-400">You don't have any notifications yet.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Auto-hide success/error messages
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 5000);

    setTimeout(function() {
        $('.alert-danger').fadeOut('slow');
    }, 7000);
});

// Mark notification as read
function markAsRead(notificationId) {
    $.ajax({
        url: `/department/notifications/${notificationId}/mark-read`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            }
        },
        error: function() {
            alert('Failed to mark notification as read');
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ucua-fyp\resources\views/department/notifications.blade.php ENDPATH**/ ?>