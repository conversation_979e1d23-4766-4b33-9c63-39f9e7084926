<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['comment', 'report', 'maxDepth' => 5, 'currentDepth' => 0]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['comment', 'report', 'maxDepth' => 5, 'currentDepth' => 0]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="threaded-comment <?php echo e($currentDepth > 0 ? 'ml-' . min($currentDepth * 4, 20) : ''); ?> mb-4">
    <div class="bg-white rounded-lg shadow-sm border <?php echo e($comment->isDepartmentRemark() ? 'border-green-200' : 'border-gray-200'); ?> p-4">
        <!-- Comment Header -->
        <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-2">
                <!-- Author Avatar/Icon -->
                <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold
                    <?php echo e($comment->user_type === 'ucua_officer' ? 'bg-blue-500' : 
                       ($comment->user_type === 'admin' ? 'bg-purple-500' : 
                       ($comment->user_type === 'department' ? 'bg-green-500' : 'bg-gray-500'))); ?>">
                    <?php echo e(substr($comment->authorName, 0, 1)); ?>

                </div>
                
                <!-- Author Info -->
                <div>
                    <div class="font-semibold text-gray-900 text-sm">
                        <?php echo e($comment->authorName); ?>

                        <?php if($comment->isDepartmentRemark()): ?>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">
                                <i class="fas fa-lock mr-1"></i>
                                Confidential
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="text-xs text-gray-500">
                        <?php echo e($comment->created_at->diffForHumans()); ?>

                        <?php if($comment->is_edited): ?>
                            <span class="text-gray-400">(edited)</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Thread Level Indicator -->
            <?php if($currentDepth > 0): ?>
                <div class="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                    Level <?php echo e($currentDepth); ?>

                </div>
            <?php endif; ?>
        </div>

        <!-- Comment Content -->
        <div class="prose prose-sm max-w-none mb-3">
            <p class="text-gray-700 whitespace-pre-wrap"><?php echo e($comment->content); ?></p>
        </div>

        <!-- Attachments -->
        <?php if($comment->attachments && $comment->attachments->count() > 0): ?>
            <div class="mb-3">
                <div class="text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-paperclip mr-1"></i>
                    Attachments (<?php echo e($comment->attachments->count()); ?>)
                </div>
                <div class="space-y-2">
                    <?php $__currentLoopData = $comment->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded border">
                            <i class="<?php echo e($attachment->iconClass); ?>"></i>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">
                                    <?php echo e($attachment->original_name); ?>

                                </div>
                                <div class="text-xs text-gray-500">
                                    <?php echo e($attachment->formattedSize); ?>

                                </div>
                            </div>
                            <a href="<?php echo e($attachment->url); ?>" 
                               target="_blank"
                               class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                <i class="fas fa-download mr-1"></i>
                                Download
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Comment Actions -->
        <div class="flex items-center justify-between pt-2 border-t border-gray-100">
            <div class="flex items-center space-x-4">
                <!-- Reply Button -->
                <?php if($currentDepth < $maxDepth): ?>
                    <button type="button"
                            class="text-sm text-green-600 hover:text-green-800 font-medium reply-btn"
                            data-comment-id="<?php echo e($comment->id); ?>"
                            data-author-name="<?php echo e($comment->authorName); ?>"
                            onclick="console.log('Department reply button clicked for comment <?php echo e($comment->id); ?>'); toggleDepartmentReplyForm(<?php echo e($comment->id); ?>);">
                        <i class="fas fa-reply mr-1"></i>
                        Reply
                    </button>
                <?php endif; ?>
                
                <!-- Reply Count -->
                <?php if($comment->reply_count > 0): ?>
                    <span class="text-sm text-gray-500">
                        <i class="fas fa-comments mr-1"></i>
                        <?php echo e($comment->reply_count); ?> <?php echo e(Str::plural('reply', $comment->reply_count)); ?>

                    </span>
                <?php endif; ?>
            </div>
            
            <!-- Timestamp -->
            <div class="text-xs text-gray-400">
                <?php echo e($comment->created_at->format('M j, Y \a\t g:i A')); ?>

            </div>
        </div>
    </div>

    <!-- Reply Form (Hidden by default) -->
    <div class="reply-form mt-3 hidden" id="reply-form-<?php echo e($comment->id); ?>">
        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <form method="POST" action="<?php echo e(route('department.add-remarks')); ?>" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="report_id" value="<?php echo e($report->id); ?>">
                <input type="hidden" name="parent_id" value="<?php echo e($comment->id); ?>">
                
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Replying to <?php echo e($comment->authorName); ?>

                    </label>
                    <textarea name="remarks" 
                              rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              placeholder="Write your reply..."
                              required></textarea>
                </div>
                
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Attachment (optional)
                    </label>
                    <input type="file" 
                           name="attachment"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                           accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt">
                    <p class="text-xs text-gray-500 mt-1">
                        Max 10MB. Allowed: JPG, PNG, PDF, DOC, XLS, TXT
                    </p>
                </div>
                
                <div class="flex items-center justify-end space-x-2">
                    <button type="button" 
                            class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 cancel-reply-btn">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-reply mr-1"></i>
                        Post Reply
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Nested Replies -->
    <?php if($comment->replies && $comment->replies->count() > 0 && $currentDepth < $maxDepth): ?>
        <div class="replies mt-4 space-y-3">
            <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if (isset($component)) { $__componentOriginalcccc45765decdfd6441f3be64c787cf1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcccc45765decdfd6441f3be64c787cf1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.department-threaded-comment','data' => ['comment' => $reply,'report' => $report,'maxDepth' => $maxDepth,'currentDepth' => $currentDepth + 1]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('department-threaded-comment'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($reply),'report' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report),'maxDepth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($maxDepth),'currentDepth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentDepth + 1)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcccc45765decdfd6441f3be64c787cf1)): ?>
<?php $attributes = $__attributesOriginalcccc45765decdfd6441f3be64c787cf1; ?>
<?php unset($__attributesOriginalcccc45765decdfd6441f3be64c787cf1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcccc45765decdfd6441f3be64c787cf1)): ?>
<?php $component = $__componentOriginalcccc45765decdfd6441f3be64c787cf1; ?>
<?php unset($__componentOriginalcccc45765decdfd6441f3be64c787cf1); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>
</div>

<?php if (! $__env->hasRenderedOnce('4db8a83e-cf56-4646-a12b-6ef478d97285')): $__env->markAsRenderedOnce('4db8a83e-cf56-4646-a12b-6ef478d97285'); ?>
<?php $__env->startPush('scripts'); ?>
<script>
// Department comment reply functionality
function toggleDepartmentReplyForm(commentId) {
    console.log('toggleDepartmentReplyForm called for comment:', commentId);

    const replyForm = document.getElementById(`reply-form-${commentId}`);

    if (!replyForm) {
        console.error('Reply form not found for comment:', commentId);
        return;
    }

    // Hide all other reply forms
    document.querySelectorAll('.reply-form').forEach(form => {
        if (form.id !== `reply-form-${commentId}`) {
            form.classList.add('hidden');
        }
    });

    // Toggle current reply form
    replyForm.classList.toggle('hidden');

    // Focus on textarea if showing
    if (!replyForm.classList.contains('hidden')) {
        const textarea = replyForm.querySelector('textarea[name="remarks"]');
        if (textarea) {
            setTimeout(() => textarea.focus(), 100);
        }
    }
}

// Handle cancel reply button clicks for department
document.addEventListener('click', function(event) {
    if (event.target.closest('.cancel-reply-btn')) {
        event.preventDefault();
        const button = event.target.closest('.cancel-reply-btn');
        const replyForm = button.closest('.reply-form');

        if (replyForm) {
            replyForm.classList.add('hidden');

            // Clear form
            const form = replyForm.querySelector('form');
            if (form) {
                form.reset();
            }
        }
    }
});

console.log('Department comment reply functionality loaded');
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\ucua-fyp\resources\views/components/department-threaded-comment.blade.php ENDPATH**/ ?>