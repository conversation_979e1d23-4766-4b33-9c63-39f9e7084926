<?php $__env->startSection('content'); ?>
<div class="flex-1 flex flex-col">
    <!-- Header -->
    <header class="bg-blue-800 text-white p-4 shadow-md">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('department.dashboard')); ?>" class="text-white hover:text-gray-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <h1 class="text-2xl font-bold">Report Details - RPT-<?php echo e(str_pad($report->id, 3, '0', STR_PAD_LEFT)); ?></h1>
            </div>
            <div class="flex items-center space-x-4">
                <span>Welcome, <?php echo e($department->head_name); ?></span>
                <form action="<?php echo e(route('department.logout')); ?>" method="POST" class="inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="text-white hover:text-gray-200">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </form>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 p-6 bg-gray-100">
        <!-- Success/Error Messages -->
        <?php if(session('success')): ?>
            <div class="alert alert-success mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
                <i class="fas fa-check-circle mr-2"></i><?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Report Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Report Details Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-start mb-4">
                        <h2 class="text-xl font-bold text-gray-800">Report Information</h2>
                        <span class="px-3 py-1 text-sm font-semibold rounded-full 
                            <?php echo e($report->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                               ($report->status === 'resolved' ? 'bg-green-100 text-green-800' : 
                                ($report->status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 
                                 'bg-gray-100 text-gray-800'))); ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $report->status))); ?>

                        </span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Report ID</label>
                            <p class="text-gray-900"><?php echo e($report->display_id); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Employee ID</label>
                            <p class="text-gray-900"><?php echo e($report->employee_id); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Reporter</label>
                            <p class="text-gray-900"><?php echo e($report->is_anonymous ? 'Anonymous' : $report->user->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                            <p class="text-gray-900"><?php echo e($report->department); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                            <p class="text-gray-900"><?php echo e($report->phone); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <p class="text-gray-900"><?php echo e($report->category ? ucfirst(str_replace('_', ' ', $report->category)) : 'Not categorized'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                            <p class="text-gray-900"><?php echo e($report->location); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Incident Date</label>
                            <p class="text-gray-900"><?php echo e($report->incident_date->format('M d, Y g:i A')); ?></p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <div class="bg-gray-50 p-3 rounded border">
                            <p class="text-gray-900 whitespace-pre-wrap"><?php echo e($report->description); ?></p>
                        </div>
                    </div>

                    <!-- Safety Details -->
                    <?php if($report->unsafe_condition || $report->unsafe_act): ?>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Safety Issue Details</label>
                        <?php if($report->unsafe_condition): ?>
                        <div class="mb-2">
                            <span class="text-sm font-medium text-red-600">Unsafe Condition:</span>
                            <span class="text-gray-900"><?php echo e($report->unsafe_condition); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if($report->unsafe_act): ?>
                        <div>
                            <span class="text-sm font-medium text-orange-600">Unsafe Act:</span>
                            <span class="text-gray-900"><?php echo e($report->unsafe_act); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if($report->attachment): ?>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Attachment</label>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-paperclip text-gray-500"></i>
                            <a href="<?php echo e(asset('storage/' . $report->attachment)); ?>" target="_blank" class="text-blue-600 hover:text-blue-800 underline">
                                View Attachment
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Department Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">Department Actions</h3>
                    <div class="flex flex-wrap gap-3">
                        <button onclick="addRemarks(<?php echo e($report->id); ?>, '<?php echo e($report->status); ?>', 'RPT-<?php echo e(str_pad($report->id, 3, '0', STR_PAD_LEFT)); ?>')" 
                                class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200">
                            <i class="fas fa-comment mr-2"></i>
                            Add Department Remark
                        </button>

                        <?php if($report->status !== 'resolved'): ?>
                        <button onclick="markAsResolved(<?php echo e($report->id); ?>, '<?php echo e($report->status); ?>', 'RPT-<?php echo e(str_pad($report->id, 3, '0', STR_PAD_LEFT)); ?>')" 
                                class="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors duration-200">
                            <i class="fas fa-check mr-2"></i>
                            Mark as Resolved
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Assignment Information -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">Assignment Details</h3>
                    
                    <div class="mb-4">
                        <span class="text-sm text-gray-500">Assigned to:</span>
                        <div class="text-base font-medium text-gray-800"><?php echo e($department->name); ?></div>
                    </div>
                    
                    <?php if($report->deadline): ?>
                    <div class="mb-4">
                        <span class="text-sm text-gray-500">Deadline:</span>
                        <div class="text-base font-medium text-gray-800">
                            <?php echo e($report->deadline->format('M d, Y')); ?>

                            <?php if($report->deadline < now()): ?>
                                <span class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">Overdue</span>
                            <?php elseif($report->deadline->diffInDays(now()) <= 3): ?>
                                <span class="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">Due Soon</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($report->assignment_remark): ?>
                    <div>
                        <span class="text-sm text-gray-500">Assignment Notes:</span>
                        <div class="text-sm text-gray-800 bg-gray-50 p-2 rounded mt-1"><?php echo e($report->assignment_remark); ?></div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Discussion Comments -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-gray-800">Discussion Comments</h3>
                        <button type="button"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                onclick="document.getElementById('main-comment-form').classList.toggle('hidden')">
                            <i class="fas fa-plus mr-1"></i>
                            Add Department Remark
                        </button>
                    </div>

                    <!-- Main Comment Form -->
                    <div id="main-comment-form" class="hidden mb-6 p-4 bg-gray-50 rounded-lg border">
                        <form method="POST" action="<?php echo e(route('department.add-remarks')); ?>" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="report_id" value="<?php echo e($report->id); ?>">

                            <div class="mb-3">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Department Remark</label>
                                <textarea name="remarks"
                                          rows="4"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                                          placeholder="Add your department remark (confidential)..."
                                          required></textarea>
                                <p class="text-xs text-yellow-600 mt-1">
                                    <i class="fas fa-lock mr-1"></i>
                                    This remark will be confidential and visible only to your department, UCUA officers, and administrators.
                                </p>
                            </div>

                            <div class="mb-3">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Attachment (optional)</label>
                                <input type="file"
                                       name="attachment"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                                       accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt">
                                <p class="text-xs text-gray-500 mt-1">
                                    Max 10MB. Allowed: JPG, PNG, PDF, DOC, XLS, TXT
                                </p>
                            </div>

                            <div class="flex items-center justify-end space-x-2">
                                <button type="button"
                                        class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                                        onclick="document.getElementById('main-comment-form').classList.add('hidden')">
                                    Cancel
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <i class="fas fa-comment mr-1"></i>
                                    Post Remark
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Threaded Comments Display -->
                    <?php if(isset($threadedRemarks) && $threadedRemarks->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $threadedRemarks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if (isset($component)) { $__componentOriginalcccc45765decdfd6441f3be64c787cf1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcccc45765decdfd6441f3be64c787cf1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.department-threaded-comment','data' => ['comment' => $comment,'report' => $report]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('department-threaded-comment'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment),'report' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($report)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcccc45765decdfd6441f3be64c787cf1)): ?>
<?php $attributes = $__attributesOriginalcccc45765decdfd6441f3be64c787cf1; ?>
<?php unset($__attributesOriginalcccc45765decdfd6441f3be64c787cf1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcccc45765decdfd6441f3be64c787cf1)): ?>
<?php $component = $__componentOriginalcccc45765decdfd6441f3be64c787cf1; ?>
<?php unset($__componentOriginalcccc45765decdfd6441f3be64c787cf1); ?>
<?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-comments text-gray-300 text-4xl mb-3"></i>
                            <p class="text-gray-500 text-sm">No discussion comments yet.</p>
                            <p class="text-gray-400 text-xs">Be the first to start the conversation!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Timeline -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4 text-gray-800">Report Timeline</h3>
                    <div class="space-y-3">
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                            <div>
                                <div class="font-medium">Report Created</div>
                                <div class="text-gray-500"><?php echo e($report->created_at->format('M d, Y g:i A')); ?></div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                            <div>
                                <div class="font-medium">Assigned to <?php echo e($department->name); ?></div>
                                <div class="text-gray-500"><?php echo e($report->updated_at->format('M d, Y g:i A')); ?></div>
                            </div>
                        </div>
                        <?php if($report->status === 'resolved'): ?>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                            <div>
                                <div class="font-medium">Report Resolved</div>
                                <div class="text-gray-500"><?php echo e($report->resolved_at ? $report->resolved_at->format('M d, Y g:i A') : 'Recently'); ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Include Modals -->
<?php echo $__env->make('department.partials.add-remarks-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php echo $__env->make('department.partials.resolve-report-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Department-specific modal handling
$(document).ready(function() {
    // Ensure all modal cancel buttons work
    $('.modal').on('click', '[data-dismiss="modal"]', function(e) {
        e.stopPropagation();
        const modalId = $(this).closest('.modal').attr('id');
        $('#' + modalId).modal('hide');
    });

    // Handle modal close buttons specifically
    $('.modal .close, .modal .btn-secondary').on('click', function(e) {
        e.stopPropagation();
        const modalId = $(this).closest('.modal').attr('id');
        $('#' + modalId).modal('hide');
    });

    // Auto-hide success/error messages
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 5000);

    setTimeout(function() {
        $('.alert-danger').fadeOut('slow');
    }, 7000);
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ucua-fyp\resources\views/department/report-detail.blade.php ENDPATH**/ ?>